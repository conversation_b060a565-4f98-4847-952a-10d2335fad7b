# Performance Guidelines

## Code Performance

### Function Optimization

```typescript
// Prefer early returns to reduce nesting
function processUser(user: User): ProcessedUser | null {
  if (!user) return null
  if (!user.isActive) return null
  if (!user.email) return null
  
  return {
    id: user.id,
    name: user.name,
    email: user.email.toLowerCase()
  }
}

// Use efficient array methods
function findActiveUsers(users: User[]): User[] {
  // Good: Single pass with filter
  return users.filter(user => user.isActive && user.lastLogin > thirtyDaysAgo)
  
  // Avoid: Multiple passes
  // return users.filter(user => user.isActive).filter(user => user.lastLogin > thirtyDaysAgo)
}

// Optimize loops for large datasets
function processLargeDataset(items: Item[]): ProcessedItem[] {
  const result: ProcessedItem[] = []
  const length = items.length
  
  // Pre-allocate array size if known
  result.length = length
  
  for (let i = 0; i < length; i++) {
    result[i] = processItem(items[i])
  }
  
  return result
}
```

### Memory Management

```typescript
// Avoid memory leaks with proper cleanup
class DataProcessor {
  private cache = new Map<string, any>()
  private timers: NodeJS.Timeout[] = []
  
  process(data: any[]): void {
    // Clear old cache entries periodically
    if (this.cache.size > 1000) {
      this.clearOldCacheEntries()
    }
    
    // Process data...
  }
  
  private clearOldCacheEntries(): void {
    const cutoff = Date.now() - 3600000 // 1 hour
    for (const [key, value] of this.cache.entries()) {
      if (value.timestamp < cutoff) {
        this.cache.delete(key)
      }
    }
  }
  
  destroy(): void {
    // Clean up resources
    this.cache.clear()
    this.timers.forEach(timer => clearTimeout(timer))
    this.timers = []
  }
}

// Use WeakMap for object associations to prevent memory leaks
const objectMetadata = new WeakMap<object, Metadata>()

function setMetadata(obj: object, metadata: Metadata): void {
  objectMetadata.set(obj, metadata)
}
```

### Async Operations

```typescript
// Batch async operations efficiently
async function processUsersInBatches(userIds: string[], batchSize = 10): Promise<User[]> {
  const results: User[] = []
  
  for (let i = 0; i < userIds.length; i += batchSize) {
    const batch = userIds.slice(i, i + batchSize)
    const batchResults = await Promise.all(
      batch.map(id => fetchUser(id))
    )
    results.push(...batchResults)
  }
  
  return results
}

// Use Promise.allSettled for error resilience
async function fetchMultipleResources(urls: string[]): Promise<Resource[]> {
  const results = await Promise.allSettled(
    urls.map(url => fetch(url).then(r => r.json()))
  )
  
  return results
    .filter((result): result is PromiseFulfilledResult<Resource> => 
      result.status === 'fulfilled'
    )
    .map(result => result.value)
}

// Implement timeout for long-running operations
async function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error('Operation timed out')), timeoutMs)
  })
  
  return Promise.race([promise, timeoutPromise])
}
```

## Database Performance

### Query Optimization

```typescript
// Use indexes effectively
interface UserQuery {
  email?: string
  status?: 'active' | 'inactive'
  createdAfter?: Date
}

// Good: Query with proper indexing
async function findUsers(query: UserQuery): Promise<User[]> {
  const conditions: any = {}
  
  if (query.email) {
    conditions.email = query.email // Indexed field
  }
  
  if (query.status) {
    conditions.status = query.status // Indexed field
  }
  
  if (query.createdAfter) {
    conditions.createdAt = { $gte: query.createdAfter } // Indexed field
  }
  
  return User.find(conditions)
    .limit(100) // Always limit results
    .sort({ createdAt: -1 }) // Use indexed sort
}

// Avoid N+1 queries with proper joins/includes
async function getUsersWithPosts(): Promise<UserWithPosts[]> {
  // Good: Single query with join
  return User.findAll({
    include: [{ model: Post, as: 'posts' }]
  })
  
  // Bad: N+1 queries
  // const users = await User.findAll()
  // for (const user of users) {
  //   user.posts = await Post.findAll({ where: { userId: user.id } })
  // }
}
```

### Connection Pooling

```typescript
// Configure database connection pool
const dbConfig = {
  pool: {
    min: 2,           // Minimum connections
    max: 10,          // Maximum connections
    acquire: 30000,   // Maximum time to get connection (ms)
    idle: 10000,      // Maximum idle time (ms)
    evict: 1000,      // Check for idle connections interval (ms)
    handleDisconnects: true
  },
  dialectOptions: {
    connectTimeout: 60000,
    acquireTimeout: 60000,
    timeout: 60000
  }
}

// Use transactions for related operations
async function transferFunds(fromId: string, toId: string, amount: number): Promise<void> {
  const transaction = await db.transaction()
  
  try {
    await Account.decrement('balance', {
      by: amount,
      where: { id: fromId },
      transaction
    })
    
    await Account.increment('balance', {
      by: amount,
      where: { id: toId },
      transaction
    })
    
    await transaction.commit()
  } catch (error) {
    await transaction.rollback()
    throw error
  }
}
```

## Caching Strategies

### In-Memory Caching

```typescript
import NodeCache from 'node-cache'

class CacheService {
  private cache = new NodeCache({
    stdTTL: 600,        // 10 minutes default TTL
    checkperiod: 120,   // Check for expired keys every 2 minutes
    useClones: false,   // Don't clone objects (better performance)
    deleteOnExpire: true
  })
  
  get<T>(key: string): T | undefined {
    return this.cache.get<T>(key)
  }
  
  set<T>(key: string, value: T, ttl?: number): boolean {
    return this.cache.set(key, value, ttl)
  }
  
  // Cache with fallback to data source
  async getOrFetch<T>(
    key: string,
    fetchFn: () => Promise<T>,
    ttl = 600
  ): Promise<T> {
    const cached = this.get<T>(key)
    if (cached !== undefined) {
      return cached
    }
    
    const value = await fetchFn()
    this.set(key, value, ttl)
    return value
  }
  
  // Invalidate related cache entries
  invalidatePattern(pattern: string): void {
    const keys = this.cache.keys()
    const regex = new RegExp(pattern)
    
    keys.forEach(key => {
      if (regex.test(key)) {
        this.cache.del(key)
      }
    })
  }
}

// Usage example
const cache = new CacheService()

async function getUser(id: string): Promise<User> {
  return cache.getOrFetch(
    `user:${id}`,
    () => User.findById(id),
    300 // 5 minutes TTL
  )
}
```

### Redis Caching

```typescript
import Redis from 'ioredis'

class RedisCache {
  private redis: Redis
  
  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT || '6379'),
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    })
  }
  
  async get<T>(key: string): Promise<T | null> {
    const value = await this.redis.get(key)
    return value ? JSON.parse(value) : null
  }
  
  async set(key: string, value: any, ttlSeconds = 600): Promise<void> {
    await this.redis.setex(key, ttlSeconds, JSON.stringify(value))
  }
  
  async invalidate(pattern: string): Promise<void> {
    const keys = await this.redis.keys(pattern)
    if (keys.length > 0) {
      await this.redis.del(...keys)
    }
  }
  
  // Distributed locking for cache warming
  async withLock<T>(
    lockKey: string,
    fn: () => Promise<T>,
    ttlSeconds = 30
  ): Promise<T> {
    const lockValue = Math.random().toString(36)
    const acquired = await this.redis.set(lockKey, lockValue, 'EX', ttlSeconds, 'NX')
    
    if (!acquired) {
      throw new Error('Could not acquire lock')
    }
    
    try {
      return await fn()
    } finally {
      // Release lock only if we still own it
      const script = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
          return redis.call("del", KEYS[1])
        else
          return 0
        end
      `
      await this.redis.eval(script, 1, lockKey, lockValue)
    }
  }
}
```

## Frontend Performance

### Bundle Optimization

```typescript
// Code splitting with dynamic imports
const LazyComponent = lazy(() => import('./HeavyComponent'))

// Route-based code splitting
const routes = [
  {
    path: '/dashboard',
    component: lazy(() => import('./pages/Dashboard'))
  },
  {
    path: '/profile',
    component: lazy(() => import('./pages/Profile'))
  }
]

// Preload critical resources
function preloadRoute(routePath: string): void {
  const route = routes.find(r => r.path === routePath)
  if (route) {
    // Preload the component
    route.component()
  }
}

// Tree shaking optimization
// Import only what you need
import { debounce } from 'lodash-es' // Good
// import _ from 'lodash' // Bad - imports entire library
```

### React Performance

```typescript
import { memo, useMemo, useCallback, useState } from 'react'

// Memoize expensive components
const ExpensiveComponent = memo<Props>(({ data, onUpdate }) => {
  const processedData = useMemo(() => {
    return data.map(item => expensiveTransformation(item))
  }, [data])
  
  const handleUpdate = useCallback((id: string, value: any) => {
    onUpdate(id, value)
  }, [onUpdate])
  
  return (
    <div>
      {processedData.map(item => (
        <Item key={item.id} data={item} onUpdate={handleUpdate} />
      ))}
    </div>
  )
})

// Optimize list rendering
function VirtualizedList({ items }: { items: Item[] }) {
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 50 })
  
  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.start, visibleRange.end)
  }, [items, visibleRange])
  
  return (
    <div onScroll={handleScroll}>
      {visibleItems.map(item => (
        <ListItem key={item.id} item={item} />
      ))}
    </div>
  )
}
```

## API Performance

### Response Optimization

```typescript
// Implement pagination
interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

async function getUsers(params: PaginationParams): Promise<PaginatedResponse<User>> {
  const { page, limit, sortBy = 'createdAt', sortOrder = 'desc' } = params
  const offset = (page - 1) * limit
  
  const [users, total] = await Promise.all([
    User.findAll({
      limit,
      offset,
      order: [[sortBy, sortOrder.toUpperCase()]]
    }),
    User.count()
  ])
  
  return {
    data: users,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  }
}

// Implement field selection
async function getUserById(id: string, fields?: string[]): Promise<Partial<User>> {
  const attributes = fields || ['id', 'name', 'email', 'createdAt']
  
  return User.findByPk(id, { attributes })
}

// Response compression
import compression from 'compression'

app.use(compression({
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false
    }
    return compression.filter(req, res)
  },
  threshold: 1024 // Only compress responses > 1KB
}))
```

### Request Optimization

```typescript
// Implement request deduplication
class RequestDeduplicator {
  private pendingRequests = new Map<string, Promise<any>>()
  
  async dedupe<T>(key: string, fn: () => Promise<T>): Promise<T> {
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key)!
    }
    
    const promise = fn().finally(() => {
      this.pendingRequests.delete(key)
    })
    
    this.pendingRequests.set(key, promise)
    return promise
  }
}

// Batch API requests
class BatchProcessor {
  private batch: Array<{ id: string; resolve: Function; reject: Function }> = []
  private timeoutId: NodeJS.Timeout | null = null
  
  async batchRequest(id: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.batch.push({ id, resolve, reject })
      
      if (this.timeoutId) {
        clearTimeout(this.timeoutId)
      }
      
      this.timeoutId = setTimeout(() => this.processBatch(), 10)
    })
  }
  
  private async processBatch(): Promise<void> {
    const currentBatch = this.batch.splice(0)
    const ids = currentBatch.map(item => item.id)
    
    try {
      const results = await this.fetchMultiple(ids)
      currentBatch.forEach((item, index) => {
        item.resolve(results[index])
      })
    } catch (error) {
      currentBatch.forEach(item => item.reject(error))
    }
  }
  
  private async fetchMultiple(ids: string[]): Promise<any[]> {
    // Implement batch fetch logic
    return []
  }
}
```

## Monitoring and Profiling

### Performance Monitoring

```typescript
import { performance } from 'node:perf_hooks'

// Function execution timing
function measurePerformance<T>(name: string, fn: () => T): T {
  const start = performance.now()
  const result = fn()
  const end = performance.now()
  
  console.log(`${name} took ${end - start} milliseconds`)
  return result
}

// Async function timing
async function measureAsyncPerformance<T>(
  name: string,
  fn: () => Promise<T>
): Promise<T> {
  const start = performance.now()
  const result = await fn()
  const end = performance.now()
  
  console.log(`${name} took ${end - start} milliseconds`)
  return result
}

// Memory usage monitoring
function logMemoryUsage(label: string): void {
  const usage = process.memoryUsage()
  console.log(`${label} Memory Usage:`, {
    rss: `${Math.round(usage.rss / 1024 / 1024)} MB`,
    heapTotal: `${Math.round(usage.heapTotal / 1024 / 1024)} MB`,
    heapUsed: `${Math.round(usage.heapUsed / 1024 / 1024)} MB`,
    external: `${Math.round(usage.external / 1024 / 1024)} MB`
  })
}
```

## Performance Best Practices

### General Guidelines

- **Measure First**: Profile before optimizing
- **Optimize Bottlenecks**: Focus on the slowest parts
- **Cache Strategically**: Cache expensive operations
- **Lazy Load**: Load resources when needed
- **Batch Operations**: Group similar operations
- **Use Indexes**: Optimize database queries
- **Minimize Payload**: Send only necessary data
- **Compress Responses**: Use gzip/brotli compression

### Code Optimization

- Avoid premature optimization
- Use efficient algorithms and data structures
- Minimize object creation in hot paths
- Use appropriate data types
- Avoid deep nesting and complex conditionals
- Implement proper error handling
- Use streaming for large data processing

### Database Optimization

- Design efficient schemas
- Use appropriate indexes
- Optimize query patterns
- Implement connection pooling
- Use read replicas for scaling
- Monitor query performance
- Implement proper caching layers
