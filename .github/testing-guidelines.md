# Testing Guidelines

## Testing Strategy

### Testing Pyramid

1. **Unit Tests (70%)**: Test individual functions and components
2. **Integration Tests (20%)**: Test component interactions
3. **End-to-End Tests (10%)**: Test complete user workflows

### Test Types

- **Unit Tests**: Functions, classes, components in isolation
- **Integration Tests**: API endpoints, database interactions, component integration
- **Contract Tests**: API contracts between services
- **Performance Tests**: Load testing, memory usage
- **Security Tests**: Vulnerability scanning, penetration testing

## Testing Framework Setup

### Recommended Stack

- **Test Runner**: Vitest (fast, TypeScript support)
- **Assertion Library**: Built-in Vitest assertions
- **Mocking**: Vitest mocks and vi utilities
- **Component Testing**: @testing-library/react (if React)
- **E2E Testing**: Playwright or Cypress

### Configuration Example

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      threshold: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  }
})
```

## Unit Testing

### File Naming

- Test files: `*.test.ts` or `*.spec.ts`
- Place tests next to source files or in `__tests__` folder
- Mirror source directory structure in test directories

### Test Structure

Use the AAA pattern (Arrange, Act, Assert):

```typescript
describe('UserService', () => {
  describe('createUser', () => {
    it('should create user with valid data', () => {
      // Arrange
      const userData = { name: 'John', email: '<EMAIL>' }
      const mockRepository = vi.fn()
      
      // Act
      const result = userService.createUser(userData)
      
      // Assert
      expect(result).toEqual(expectedUser)
      expect(mockRepository).toHaveBeenCalledWith(userData)
    })
  })
})
```

### Test Naming Convention

```typescript
// Pattern: should [expected behavior] when [condition]
it('should return user data when valid ID is provided', () => {})
it('should throw error when user not found', () => {})
it('should validate email format before creating user', () => {})
```

### What to Test

**Do Test:**
- Public API methods
- Edge cases and error conditions
- Business logic and calculations
- Data transformations
- Validation logic

**Don't Test:**
- Private methods directly
- Third-party library internals
- Simple getters/setters
- Framework code

## Mocking Guidelines

### Mock External Dependencies

```typescript
// Mock API calls
vi.mock('./api-client', () => ({
  fetchUser: vi.fn(),
  createUser: vi.fn()
}))

// Mock modules
vi.mock('node:fs', () => ({
  readFileSync: vi.fn(),
  writeFileSync: vi.fn()
}))
```

### Mock Best Practices

- Mock at the boundary (external APIs, databases)
- Use real objects for internal logic
- Reset mocks between tests
- Verify mock interactions when relevant
- Keep mocks simple and focused

## Component Testing

### React Component Testing

```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { UserProfile } from './UserProfile'

describe('UserProfile', () => {
  it('should display user information', () => {
    const user = { name: 'John Doe', email: '<EMAIL>' }
    
    render(<UserProfile user={user} />)
    
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })
  
  it('should handle edit button click', () => {
    const onEdit = vi.fn()
    const user = { name: 'John Doe', email: '<EMAIL>' }
    
    render(<UserProfile user={user} onEdit={onEdit} />)
    
    fireEvent.click(screen.getByRole('button', { name: /edit/i }))
    
    expect(onEdit).toHaveBeenCalledWith(user)
  })
})
```

### Component Testing Best Practices

- Test behavior, not implementation
- Use semantic queries (getByRole, getByLabelText)
- Test user interactions
- Mock external dependencies
- Test accessibility features

## Integration Testing

### API Integration Tests

```typescript
describe('User API Integration', () => {
  beforeEach(async () => {
    await setupTestDatabase()
  })
  
  afterEach(async () => {
    await cleanupTestDatabase()
  })
  
  it('should create and retrieve user', async () => {
    const userData = { name: 'John', email: '<EMAIL>' }
    
    const createResponse = await request(app)
      .post('/api/users')
      .send(userData)
      .expect(201)
    
    const userId = createResponse.body.id
    
    const getResponse = await request(app)
      .get(`/api/users/${userId}`)
      .expect(200)
    
    expect(getResponse.body).toMatchObject(userData)
  })
})
```

### Database Testing

- Use test database or in-memory database
- Clean up data between tests
- Test database constraints and validations
- Test transaction handling

## Test Data Management

### Test Fixtures

```typescript
// test/fixtures/users.ts
export const validUser = {
  name: 'John Doe',
  email: '<EMAIL>',
  age: 30
}

export const invalidUser = {
  name: '',
  email: 'invalid-email',
  age: -5
}
```

### Factory Functions

```typescript
// test/factories/user-factory.ts
export const createUser = (overrides = {}) => ({
  id: Math.random().toString(),
  name: 'Test User',
  email: '<EMAIL>',
  createdAt: new Date(),
  ...overrides
})
```

## Test Organization

### Test Structure

```
tests/
├── unit/
│   ├── services/
│   ├── utils/
│   └── components/
├── integration/
│   ├── api/
│   └── database/
├── e2e/
│   ├── user-flows/
│   └── critical-paths/
├── fixtures/
├── factories/
└── helpers/
```

### Test Helpers

```typescript
// test/helpers/test-utils.ts
export const renderWithProviders = (ui: ReactElement, options = {}) => {
  const AllTheProviders = ({ children }) => (
    <ThemeProvider>
      <AuthProvider>
        {children}
      </AuthProvider>
    </ThemeProvider>
  )
  
  return render(ui, { wrapper: AllTheProviders, ...options })
}
```

## Coverage Requirements

### Coverage Targets

- **Statements**: 80% minimum
- **Branches**: 80% minimum
- **Functions**: 80% minimum
- **Lines**: 80% minimum

### Coverage Exclusions

```typescript
// Exclude from coverage
/* istanbul ignore next */
if (process.env.NODE_ENV === 'development') {
  // Development-only code
}
```

## Performance Testing

### Load Testing

```typescript
// Simple performance test
describe('Performance Tests', () => {
  it('should process large dataset efficiently', () => {
    const largeDataset = Array.from({ length: 10000 }, (_, i) => ({ id: i }))
    
    const startTime = performance.now()
    const result = processData(largeDataset)
    const endTime = performance.now()
    
    expect(endTime - startTime).toBeLessThan(1000) // 1 second max
    expect(result).toHaveLength(10000)
  })
})
```

## Best Practices

### General Testing

- Write tests before or alongside code (TDD/BDD)
- Keep tests simple and focused
- Use descriptive test names
- Test one thing at a time
- Make tests independent and isolated
- Use setup and teardown appropriately

### Test Maintenance

- Refactor tests when refactoring code
- Remove obsolete tests
- Keep test code clean and readable
- Update tests when requirements change
- Monitor test execution time

### Continuous Integration

- Run tests on every commit
- Fail builds on test failures
- Generate coverage reports
- Run different test suites in parallel
- Cache dependencies for faster builds

### Debugging Tests

- Use focused tests (`it.only`, `describe.only`)
- Add debug output when needed
- Use debugger statements
- Check test isolation issues
- Verify mock configurations

## Common Patterns

### Testing Async Code

```typescript
it('should handle async operations', async () => {
  const promise = asyncFunction()
  await expect(promise).resolves.toBe(expectedValue)
})

it('should handle rejected promises', async () => {
  const promise = failingAsyncFunction()
  await expect(promise).rejects.toThrow('Expected error')
})
```

### Testing Error Conditions

```typescript
it('should throw error for invalid input', () => {
  expect(() => {
    validateInput(invalidData)
  }).toThrow('Invalid input provided')
})
```

### Testing Callbacks

```typescript
it('should call callback with correct arguments', () => {
  const callback = vi.fn()
  
  processData(data, callback)
  
  expect(callback).toHaveBeenCalledWith(expectedResult)
  expect(callback).toHaveBeenCalledTimes(1)
})
```
