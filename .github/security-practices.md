# Security Best Practices

## Input Validation and Sanitization

### Data Validation

```typescript
// Always validate input data
interface CreateUserRequest {
  name: string
  email: string
  age?: number
}

function validateUserInput(data: unknown): CreateUserRequest {
  if (!data || typeof data !== 'object') {
    throw new Error('Invalid input data')
  }
  
  const { name, email, age } = data as any
  
  if (!name || typeof name !== 'string' || name.trim().length === 0) {
    throw new Error('Name is required and must be a non-empty string')
  }
  
  if (!email || typeof email !== 'string' || !isValidEmail(email)) {
    throw new Error('Valid email is required')
  }
  
  if (age !== undefined && (typeof age !== 'number' || age < 0 || age > 150)) {
    throw new Error('Age must be a valid number between 0 and 150')
  }
  
  return { name: name.trim(), email: email.toLowerCase(), age }
}
```

### Input Sanitization

```typescript
import DOMPurify from 'dompurify'
import validator from 'validator'

// Sanitize HTML content
function sanitizeHtml(input: string): string {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em'],
    ALLOWED_ATTR: []
  })
}

// Sanitize and validate email
function sanitizeEmail(email: string): string {
  const sanitized = validator.normalizeEmail(email) || ''
  if (!validator.isEmail(sanitized)) {
    throw new Error('Invalid email format')
  }
  return sanitized
}

// Escape SQL-like characters (use parameterized queries instead)
function escapeString(input: string): string {
  return input.replace(/['";\\]/g, '\\$&')
}
```

## Authentication and Authorization

### JWT Token Handling

```typescript
import jwt from 'jsonwebtoken'
import bcrypt from 'bcrypt'

interface TokenPayload {
  userId: string
  email: string
  role: string
  iat: number
  exp: number
}

class AuthService {
  private readonly JWT_SECRET = process.env.JWT_SECRET!
  private readonly JWT_EXPIRES_IN = '24h'
  private readonly SALT_ROUNDS = 12
  
  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.SALT_ROUNDS)
  }
  
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash)
  }
  
  generateToken(payload: Omit<TokenPayload, 'iat' | 'exp'>): string {
    return jwt.sign(payload, this.JWT_SECRET, {
      expiresIn: this.JWT_EXPIRES_IN,
      issuer: 'your-app-name',
      audience: 'your-app-users'
    })
  }
  
  verifyToken(token: string): TokenPayload {
    try {
      return jwt.verify(token, this.JWT_SECRET) as TokenPayload
    } catch (error) {
      throw new Error('Invalid or expired token')
    }
  }
}
```

### Role-Based Access Control

```typescript
enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  MODERATOR = 'moderator'
}

interface Permission {
  resource: string
  action: string
}

const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    { resource: 'users', action: 'create' },
    { resource: 'users', action: 'read' },
    { resource: 'users', action: 'update' },
    { resource: 'users', action: 'delete' }
  ],
  [UserRole.MODERATOR]: [
    { resource: 'users', action: 'read' },
    { resource: 'users', action: 'update' }
  ],
  [UserRole.USER]: [
    { resource: 'profile', action: 'read' },
    { resource: 'profile', action: 'update' }
  ]
}

function hasPermission(userRole: UserRole, resource: string, action: string): boolean {
  const permissions = rolePermissions[userRole] || []
  return permissions.some(p => p.resource === resource && p.action === action)
}

// Middleware for Express.js
function requirePermission(resource: string, action: string) {
  return (req: Request, res: Response, next: NextFunction) => {
    const user = req.user // Assume user is attached by auth middleware
    
    if (!user || !hasPermission(user.role, resource, action)) {
      return res.status(403).json({ error: 'Insufficient permissions' })
    }
    
    next()
  }
}
```

## Environment and Configuration Security

### Environment Variables

```typescript
// config/environment.ts
interface EnvironmentConfig {
  NODE_ENV: 'development' | 'production' | 'test'
  PORT: number
  DATABASE_URL: string
  JWT_SECRET: string
  API_KEY: string
  ENCRYPTION_KEY: string
}

function validateEnvironment(): EnvironmentConfig {
  const requiredVars = [
    'NODE_ENV',
    'DATABASE_URL',
    'JWT_SECRET',
    'API_KEY',
    'ENCRYPTION_KEY'
  ]
  
  const missing = requiredVars.filter(key => !process.env[key])
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
  
  // Validate JWT secret strength
  if (process.env.JWT_SECRET!.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long')
  }
  
  return {
    NODE_ENV: process.env.NODE_ENV as any,
    PORT: parseInt(process.env.PORT || '3000', 10),
    DATABASE_URL: process.env.DATABASE_URL!,
    JWT_SECRET: process.env.JWT_SECRET!,
    API_KEY: process.env.API_KEY!,
    ENCRYPTION_KEY: process.env.ENCRYPTION_KEY!
  }
}

export const config = validateEnvironment()
```

### Secrets Management

```bash
# .env.example (commit this)
NODE_ENV=development
PORT=3000
DATABASE_URL=postgresql://user:password@localhost:5432/dbname
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters
API_KEY=your-api-key
ENCRYPTION_KEY=your-encryption-key

# .env (never commit this)
# Copy from .env.example and fill with real values
```

## Data Protection

### Encryption

```typescript
import crypto from 'node:crypto'

class EncryptionService {
  private readonly algorithm = 'aes-256-gcm'
  private readonly keyLength = 32
  private readonly ivLength = 16
  private readonly tagLength = 16
  
  constructor(private readonly key: string) {
    if (Buffer.from(key, 'hex').length !== this.keyLength) {
      throw new Error('Encryption key must be 32 bytes (64 hex characters)')
    }
  }
  
  encrypt(text: string): string {
    const iv = crypto.randomBytes(this.ivLength)
    const cipher = crypto.createCipher(this.algorithm, Buffer.from(this.key, 'hex'))
    cipher.setAAD(Buffer.from('additional-data'))
    
    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    
    const tag = cipher.getAuthTag()
    
    return iv.toString('hex') + ':' + tag.toString('hex') + ':' + encrypted
  }
  
  decrypt(encryptedData: string): string {
    const parts = encryptedData.split(':')
    if (parts.length !== 3) {
      throw new Error('Invalid encrypted data format')
    }
    
    const [ivHex, tagHex, encrypted] = parts
    const iv = Buffer.from(ivHex, 'hex')
    const tag = Buffer.from(tagHex, 'hex')
    
    const decipher = crypto.createDecipher(this.algorithm, Buffer.from(this.key, 'hex'))
    decipher.setAAD(Buffer.from('additional-data'))
    decipher.setAuthTag(tag)
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    
    return decrypted
  }
}
```

### Sensitive Data Handling

```typescript
// Never log sensitive data
function logUserAction(userId: string, action: string, data: any) {
  const sanitizedData = { ...data }
  
  // Remove sensitive fields
  delete sanitizedData.password
  delete sanitizedData.ssn
  delete sanitizedData.creditCard
  delete sanitizedData.apiKey
  
  console.log(`User ${userId} performed ${action}`, sanitizedData)
}

// Mask sensitive data in responses
function maskEmail(email: string): string {
  const [local, domain] = email.split('@')
  const maskedLocal = local.charAt(0) + '*'.repeat(local.length - 2) + local.charAt(local.length - 1)
  return `${maskedLocal}@${domain}`
}

function maskCreditCard(cardNumber: string): string {
  return '*'.repeat(cardNumber.length - 4) + cardNumber.slice(-4)
}
```

## API Security

### Rate Limiting

```typescript
import rateLimit from 'express-rate-limit'
import RedisStore from 'rate-limit-redis'
import Redis from 'ioredis'

const redis = new Redis(process.env.REDIS_URL)

// General API rate limiting
export const apiLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args)
  }),
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later',
  standardHeaders: true,
  legacyHeaders: false
})

// Strict rate limiting for authentication endpoints
export const authLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args)
  }),
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 login attempts per windowMs
  message: 'Too many login attempts, please try again later',
  skipSuccessfulRequests: true
})
```

### CORS Configuration

```typescript
import cors from 'cors'

const corsOptions: cors.CorsOptions = {
  origin: (origin, callback) => {
    const allowedOrigins = [
      'https://yourdomain.com',
      'https://www.yourdomain.com'
    ]
    
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true)
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true)
    } else {
      callback(new Error('Not allowed by CORS'))
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization']
}

app.use(cors(corsOptions))
```

### Security Headers

```typescript
import helmet from 'helmet'

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}))

// Additional security headers
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff')
  res.setHeader('X-Frame-Options', 'DENY')
  res.setHeader('X-XSS-Protection', '1; mode=block')
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin')
  next()
})
```

## Database Security

### SQL Injection Prevention

```typescript
// Always use parameterized queries
async function getUserById(id: string): Promise<User | null> {
  // Good: Parameterized query
  const result = await db.query('SELECT * FROM users WHERE id = $1', [id])
  return result.rows[0] || null
}

// Never concatenate user input into SQL
async function badGetUserById(id: string): Promise<User | null> {
  // BAD: SQL injection vulnerability
  const result = await db.query(`SELECT * FROM users WHERE id = '${id}'`)
  return result.rows[0] || null
}

// Use query builders or ORMs
async function getUserByEmail(email: string): Promise<User | null> {
  return await User.findOne({ where: { email } }) // Sequelize ORM
}
```

### Database Connection Security

```typescript
// Database configuration with security settings
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME,
  username: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  ssl: process.env.NODE_ENV === 'production' ? {
    require: true,
    rejectUnauthorized: false
  } : false,
  pool: {
    min: 2,
    max: 10,
    acquire: 30000,
    idle: 10000
  },
  logging: process.env.NODE_ENV === 'development'
}
```

## Security Monitoring

### Logging Security Events

```typescript
import winston from 'winston'

const securityLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'security.log' }),
    new winston.transports.Console()
  ]
})

function logSecurityEvent(event: string, details: any, userId?: string) {
  securityLogger.warn('Security Event', {
    event,
    userId,
    timestamp: new Date().toISOString(),
    ip: details.ip,
    userAgent: details.userAgent,
    details
  })
}

// Usage examples
logSecurityEvent('FAILED_LOGIN', { ip: req.ip, userAgent: req.get('User-Agent') })
logSecurityEvent('SUSPICIOUS_ACTIVITY', { ip: req.ip, endpoint: req.path }, userId)
```

## Security Checklist

### Development

- [ ] Input validation on all user inputs
- [ ] Output encoding/escaping
- [ ] Parameterized database queries
- [ ] Strong password requirements
- [ ] Secure session management
- [ ] HTTPS enforcement
- [ ] Security headers implementation
- [ ] Rate limiting on APIs
- [ ] CORS configuration
- [ ] Environment variable validation

### Deployment

- [ ] Secrets stored securely (not in code)
- [ ] Database connections encrypted
- [ ] Regular security updates
- [ ] Monitoring and alerting setup
- [ ] Backup encryption
- [ ] Access logs enabled
- [ ] Firewall configuration
- [ ] SSL/TLS certificates valid

### Ongoing

- [ ] Regular dependency updates
- [ ] Security audit logs review
- [ ] Penetration testing
- [ ] Code security reviews
- [ ] Incident response plan
- [ ] Security training for team
- [ ] Vulnerability scanning
- [ ] Compliance requirements met
