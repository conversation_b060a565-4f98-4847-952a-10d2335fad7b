# Git Workflow Guidelines

## Branch Strategy

### Main Branches

- **main**: Production-ready code, always deployable
- **develop**: Integration branch for features, pre-production testing

### Supporting Branches

- **feature/**: New features (`feature/user-authentication`)
- **bugfix/**: Bug fixes (`bugfix/login-validation`)
- **hotfix/**: Critical production fixes (`hotfix/security-patch`)
- **release/**: Release preparation (`release/v1.2.0`)

### Branch Naming Convention

```
type/short-description
```

**Examples:**
- `feature/user-profile-page`
- `bugfix/memory-leak-fix`
- `hotfix/critical-security-patch`
- `release/v2.1.0`

## Commit Message Format

### Conventional Commits

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Commit Types

- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, missing semicolons, etc.)
- **refactor**: Code refactoring without changing functionality
- **perf**: Performance improvements
- **test**: Adding or updating tests
- **build**: Build system or external dependencies
- **ci**: CI/CD configuration changes
- **chore**: Maintenance tasks

### Examples

```bash
feat(auth): add user login functionality

fix(api): resolve memory leak in data processing

docs: update installation instructions

style: fix linting errors in user service

refactor(utils): simplify date formatting logic

test(auth): add unit tests for login validation

build: update dependencies to latest versions

ci: add automated testing workflow
```

### Commit Message Rules

- Use imperative mood ("add" not "added" or "adds")
- Keep subject line under 50 characters
- Capitalize subject line
- No period at the end of subject line
- Separate subject and body with blank line
- Wrap body at 72 characters
- Use body to explain what and why, not how

## Pull Request Guidelines

### PR Title Format

Use the same format as commit messages:

```
<type>[optional scope]: <description>
```

### PR Description Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or documented)
```

### PR Best Practices

- Keep PRs small and focused (< 400 lines changed)
- Include tests for new functionality
- Update documentation when needed
- Link related issues
- Request specific reviewers
- Respond to feedback promptly

## Workflow Process

### Feature Development

1. **Create branch from develop**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/new-feature
   ```

2. **Develop and commit**
   ```bash
   git add .
   git commit -m "feat: add new feature implementation"
   ```

3. **Push and create PR**
   ```bash
   git push origin feature/new-feature
   # Create PR to develop branch
   ```

4. **Code review and merge**
   - Address review feedback
   - Squash commits if needed
   - Merge to develop

### Hotfix Process

1. **Create branch from main**
   ```bash
   git checkout main
   git pull origin main
   git checkout -b hotfix/critical-fix
   ```

2. **Fix and test**
   ```bash
   git commit -m "fix: resolve critical security issue"
   ```

3. **Merge to main and develop**
   ```bash
   # PR to main for immediate deployment
   # PR to develop to keep branches in sync
   ```

## Code Review Standards

### Review Checklist

- [ ] Code follows project style guidelines
- [ ] Logic is clear and well-documented
- [ ] Tests cover new functionality
- [ ] No security vulnerabilities
- [ ] Performance considerations addressed
- [ ] Error handling implemented
- [ ] Documentation updated

### Review Guidelines

- Be constructive and respectful
- Focus on code, not the person
- Explain the "why" behind suggestions
- Approve when ready, request changes when needed
- Use GitHub's suggestion feature for small fixes

## Release Process

### Version Numbering

Follow [Semantic Versioning](https://semver.org/):

- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Steps

1. **Create release branch**
   ```bash
   git checkout develop
   git checkout -b release/v1.2.0
   ```

2. **Update version and changelog**
   - Update package.json version
   - Update CHANGELOG.md
   - Final testing

3. **Merge to main and tag**
   ```bash
   git checkout main
   git merge release/v1.2.0
   git tag v1.2.0
   git push origin main --tags
   ```

4. **Merge back to develop**
   ```bash
   git checkout develop
   git merge main
   ```

## Git Configuration

### Recommended Settings

```bash
# Set up user information
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# Useful aliases
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.st status
git config --global alias.unstage 'reset HEAD --'
git config --global alias.last 'log -1 HEAD'
git config --global alias.visual '!gitk'

# Better diff and merge tools
git config --global merge.tool vimdiff
git config --global diff.tool vimdiff

# Auto-setup remote tracking
git config --global push.autoSetupRemote true
```

### Gitignore Best Practices

Include in `.gitignore`:

```gitignore
# Dependencies
node_modules/
npm-debug.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment files
.env
.env.local
.env.*.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Temporary folders
tmp/
temp/
```

## Best Practices

### Commit Hygiene

- Commit early and often
- Make atomic commits (one logical change per commit)
- Write meaningful commit messages
- Use interactive rebase to clean up history
- Don't commit generated files or dependencies

### Branch Management

- Delete merged branches
- Keep branches up to date with base branch
- Use descriptive branch names
- Don't work directly on main/develop
- Regularly sync with remote branches

### Collaboration

- Communicate changes that affect others
- Use draft PRs for work in progress
- Tag team members for specific reviews
- Document breaking changes clearly
- Keep commit history clean and readable
