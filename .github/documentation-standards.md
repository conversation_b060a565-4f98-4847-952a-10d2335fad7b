# Documentation Standards

## Documentation Types

### Code Documentation

- **Inline Comments**: Explain complex logic and business rules
- **JSDoc Comments**: Document functions, classes, and interfaces
- **README Files**: Project overview and setup instructions
- **API Documentation**: Endpoint specifications and examples

### Project Documentation

- **Architecture Docs**: System design and component relationships
- **User Guides**: How to use the application
- **Developer Guides**: How to contribute and develop
- **Deployment Guides**: How to deploy and configure

## JSDoc Standards

### Function Documentation

```typescript
/**
 * Calculates the total price including tax and discounts
 * @param basePrice - The original price before modifications
 * @param taxRate - Tax rate as decimal (0.1 for 10%)
 * @param discountPercent - Discount percentage (10 for 10% off)
 * @returns The final price after tax and discount
 * @throws {Error} When basePrice is negative
 * @example
 * ```typescript
 * const total = calculateTotal(100, 0.1, 15)
 * console.log(total) // 93.5
 * ```
 */
function calculateTotal(
  basePrice: number,
  taxRate: number,
  discountPercent: number
): number {
  if (basePrice < 0) {
    throw new Error('Base price cannot be negative')
  }
  
  const discountAmount = basePrice * (discountPercent / 100)
  const discountedPrice = basePrice - discountAmount
  return discountedPrice * (1 + taxRate)
}
```

### Class Documentation

```typescript
/**
 * Manages user authentication and session handling
 * @example
 * ```typescript
 * const auth = new AuthService({ apiUrl: 'https://api.example.com' })
 * const user = await auth.login('<EMAIL>', 'password')
 * ```
 */
class AuthService {
  /**
   * Creates an instance of AuthService
   * @param config - Configuration options for the service
   */
  constructor(private config: AuthConfig) {}
  
  /**
   * Authenticates user with email and password
   * @param email - User's email address
   * @param password - User's password
   * @returns Promise resolving to user data
   * @throws {AuthError} When credentials are invalid
   */
  async login(email: string, password: string): Promise<User> {
    // Implementation
  }
}
```

### Interface Documentation

```typescript
/**
 * Configuration options for API client
 */
interface ApiConfig {
  /** Base URL for API requests */
  baseUrl: string
  /** API key for authentication */
  apiKey: string
  /** Request timeout in milliseconds @default 5000 */
  timeout?: number
  /** Enable request/response logging @default false */
  debug?: boolean
}
```

## README Structure

### Project README Template

```markdown
# Project Name

Brief description of what the project does and its main purpose.

## Features

- Feature 1
- Feature 2
- Feature 3

## Prerequisites

- Node.js 18+
- npm 9+
- TypeScript 5+

## Installation

```bash
npm install
```

## Quick Start

```bash
# Development
npm run dev

# Build
npm run build

# Test
npm run test
```

## Configuration

Environment variables:

- `API_URL` - API endpoint URL
- `API_KEY` - API authentication key
- `DEBUG` - Enable debug mode (true/false)

## Usage

Basic usage example:

```typescript
import { ApiClient } from './api-client'

const client = new ApiClient({
  baseUrl: 'https://api.example.com',
  apiKey: process.env.API_KEY
})

const data = await client.fetchData()
```

## API Reference

Link to detailed API documentation.

## Contributing

See [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

## License

MIT License - see [LICENSE](LICENSE) file.
```

## API Documentation

### Endpoint Documentation

```typescript
/**
 * @api {post} /api/users Create User
 * @apiName CreateUser
 * @apiGroup Users
 * @apiVersion 1.0.0
 * 
 * @apiParam {String} name User's full name
 * @apiParam {String} email User's email address
 * @apiParam {Number} [age] User's age (optional)
 * 
 * @apiSuccess {String} id User's unique identifier
 * @apiSuccess {String} name User's full name
 * @apiSuccess {String} email User's email address
 * @apiSuccess {Date} createdAt Account creation timestamp
 * 
 * @apiError {String} error Error message
 * @apiError {Number} code Error code
 * 
 * @apiExample {curl} Example Request:
 * curl -X POST https://api.example.com/users \
 *   -H "Content-Type: application/json" \
 *   -d '{"name":"John Doe","email":"<EMAIL>"}'
 * 
 * @apiExample {json} Success Response:
 * {
 *   "id": "123e4567-e89b-12d3-a456-************",
 *   "name": "John Doe",
 *   "email": "<EMAIL>",
 *   "createdAt": "2023-01-01T00:00:00.000Z"
 * }
 */
```

### OpenAPI/Swagger Documentation

Use OpenAPI 3.0 specification for REST APIs:

```yaml
openapi: 3.0.0
info:
  title: User API
  version: 1.0.0
  description: API for user management

paths:
  /users:
    post:
      summary: Create a new user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        email:
          type: string
          format: email
```

## Inline Comments

### When to Comment

**Do Comment:**
- Complex business logic
- Non-obvious algorithms
- Workarounds and hacks
- Performance-critical sections
- Security-related code
- External API integrations

**Don't Comment:**
- Obvious code
- What the code does (code should be self-explanatory)
- Redundant information
- Outdated information

### Comment Style

```typescript
// Good: Explains why, not what
// Using exponential backoff to handle rate limiting
const delay = Math.min(1000 * Math.pow(2, retryCount), 30000)

// Bad: Explains what (obvious from code)
// Increment the counter by 1
counter++

// Good: Explains business rule
// Maximum 3 login attempts before account lockout
if (failedAttempts >= 3) {
  lockAccount(userId)
}

// Good: Explains complex algorithm
// Boyer-Moore string search algorithm for efficient pattern matching
// Time complexity: O(n/m) average case, O(nm) worst case
function boyerMooreSearch(text: string, pattern: string): number {
  // Implementation
}
```

## Architecture Documentation

### Architecture Decision Records (ADRs)

```markdown
# ADR-001: Use TypeScript for Type Safety

## Status
Accepted

## Context
We need to choose a programming language for our new web application.
JavaScript lacks compile-time type checking, leading to runtime errors.

## Decision
We will use TypeScript for all new development.

## Consequences
**Positive:**
- Compile-time type checking
- Better IDE support
- Improved code documentation
- Easier refactoring

**Negative:**
- Additional build step
- Learning curve for team
- Slightly longer development time initially

## Alternatives Considered
- Plain JavaScript
- Flow
- JSDoc with TypeScript checking
```

### System Architecture

```markdown
# System Architecture

## Overview
High-level description of the system architecture.

## Components

### Frontend
- React application
- TypeScript
- Vite build tool

### Backend
- Node.js with Express
- TypeScript
- PostgreSQL database

### Infrastructure
- Docker containers
- AWS deployment
- Redis for caching

## Data Flow
1. User interacts with React frontend
2. Frontend makes API calls to backend
3. Backend processes requests and queries database
4. Response sent back to frontend

## Security
- JWT authentication
- HTTPS encryption
- Input validation
- SQL injection prevention
```

## Documentation Best Practices

### Writing Guidelines

- **Be Clear and Concise**: Use simple language and short sentences
- **Use Active Voice**: "The function returns" not "The value is returned"
- **Be Specific**: Provide exact parameter types and return values
- **Include Examples**: Show real usage examples
- **Keep Updated**: Update docs when code changes

### Structure Guidelines

- **Logical Organization**: Group related information together
- **Consistent Formatting**: Use consistent headers, lists, and code blocks
- **Table of Contents**: For longer documents
- **Cross-References**: Link to related documentation
- **Version Information**: Include version numbers and dates

### Code Examples

- **Complete Examples**: Show full, working code
- **Realistic Examples**: Use real-world scenarios
- **Error Handling**: Include error handling in examples
- **Multiple Scenarios**: Show different use cases
- **Copy-Paste Ready**: Examples should work without modification

## Documentation Tools

### Recommended Tools

- **JSDoc**: For code documentation
- **TypeDoc**: Generate docs from TypeScript
- **Swagger/OpenAPI**: For API documentation
- **Storybook**: For component documentation
- **GitBook/Notion**: For user guides

### Automation

- **Auto-generation**: Generate docs from code comments
- **CI/CD Integration**: Update docs on code changes
- **Link Checking**: Verify internal and external links
- **Spell Checking**: Automated spell checking
- **Format Validation**: Ensure consistent formatting

## Maintenance

### Regular Reviews

- Review documentation quarterly
- Update examples and screenshots
- Remove outdated information
- Check for broken links
- Verify accuracy of instructions

### Feedback Collection

- Add feedback forms to documentation
- Monitor user questions and issues
- Track documentation usage analytics
- Collect feedback from new team members
- Regular documentation surveys
